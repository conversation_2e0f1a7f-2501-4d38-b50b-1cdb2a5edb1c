# 百度地铁图在ReactScaleScreen中的适配解决方案

## 问题分析

### 根本原因
1. **ReactScaleScreen的缩放机制**：使用CSS `transform: scale()` 对整个内容进行缩放，设计尺寸为7680x2160
2. **百度地铁SDK的DOM依赖**：`BMapSub.Subway` 在初始化时读取DOM容器的实际尺寸（`offsetWidth`、`offsetHeight`）
3. **冲突点**：
   - ReactScaleScreen改变视觉尺寸，但DOM实际尺寸仍为7680x2160
   - 百度地铁SDK按照巨大的原始尺寸渲染，导致缩放后元素位置和大小错误

## 解决方案

### 核心思路
在百度地铁SDK初始化时，临时设置容器为正确的视觉尺寸，让SDK读取到准确的尺寸信息。

### 实现步骤

#### 1. 获取缩放信息
```typescript
const getScaleInfo = () => {
  // 向上查找ReactScaleScreen容器
  let element = mapRef.current?.parentElement;
  while (element) {
    const transform = window.getComputedStyle(element).transform;
    if (transform && transform !== 'none') {
      // 解析transform matrix获取缩放比例
      const matrix = transform.match(/matrix\(([^)]+)\)/);
      if (matrix) {
        const values = matrix[1].split(',').map(v => parseFloat(v.trim()));
        const scaleX = values[0];
        const scaleY = values[3];
        return { scaleX, scaleY, element };
      }
    }
    element = element.parentElement;
  }
  return { scaleX: 1, scaleY: 1, element: null };
};
```

#### 2. 临时设置正确尺寸
```typescript
// 获取容器的实际视觉尺寸
const containerRect = mapRef.current.getBoundingClientRect();
const originalWidth = containerRect.width / scaleX;
const originalHeight = containerRect.height / scaleY;

// 保存原始样式
const originalStyle = {
  width: mapRef.current.style.width,
  height: mapRef.current.style.height,
  transform: mapRef.current.style.transform,
  transformOrigin: mapRef.current.style.transformOrigin
};

// 设置为实际的视觉尺寸
mapRef.current.style.width = `${containerRect.width}px`;
mapRef.current.style.height = `${containerRect.height}px`;
mapRef.current.style.transform = 'none';

// 初始化地铁图
const subwayInstance = new window.BMapSub.Subway(
  mapRef.current,
  targetCity.citycode
);

// 恢复原始样式
mapRef.current.style.width = originalStyle.width;
mapRef.current.style.height = originalStyle.height;
mapRef.current.style.transform = originalStyle.transform;
```

#### 3. 调整缩放级别
```typescript
// 根据缩放比例调整地铁图的缩放级别
const avgScale = (scaleX + scaleY) / 2;
const baseZoom = 0.5;
const adjustedZoom = Math.max(0.1, Math.min(2, baseZoom * avgScale));

subwayInstance.setZoom(adjustedZoom);
subwayInstance.setCenter("安定门");
```

### 关键技术点

1. **CSS Transform解析**：通过解析CSS transform matrix获取准确的缩放比例
2. **临时样式修改**：在SDK初始化时临时设置正确的DOM尺寸
3. **样式恢复**：初始化完成后立即恢复原始样式
4. **缩放级别调整**：根据ReactScaleScreen的缩放比例调整地铁图缩放
5. **响应式处理**：监听窗口大小变化，重新初始化地铁图

### 样式优化

```less
.subContainer {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden; // 防止内容溢出
}

.subMain {
  width: 100%;
  height: 100%;
  background: none !important;
  position: relative;
  
  // 确保地铁图容器能够正确响应尺寸变化
  & > div {
    width: 100% !important;
    height: 100% !important;
  }
  
  // 修复百度地铁图可能的样式问题
  canvas {
    max-width: 100% !important;
    max-height: 100% !important;
  }
}
```

## 效果验证

1. 地铁图能够正确显示在指定区域内
2. 站点、线路、控件的位置和大小符合预期
3. 缩放和平移操作正常工作
4. 响应式布局正常，窗口大小变化时地铁图能够正确适配

## 适用场景

这个解决方案适用于所有需要在ReactScaleScreen或类似缩放容器中使用第三方DOM依赖SDK的场景，包括但不限于：

- 百度地图系列API
- 高德地图API
- 其他基于DOM尺寸的图表库
- Canvas绘图库

## 注意事项

1. 确保在ReactScaleScreen完全渲染后再初始化第三方SDK
2. 监听窗口大小变化，及时重新初始化
3. 正确处理SDK的清理工作，避免内存泄漏
4. 根据实际的缩放比例调整SDK的相关参数
