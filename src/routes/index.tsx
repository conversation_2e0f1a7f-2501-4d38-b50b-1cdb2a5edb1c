import AuthRoute from '@/components/AuthRoute'
import Layout from '@/layouts'
import { lazy } from 'react'
import { createBrowserRouter, Navigate } from 'react-router-dom'

const routes = createBrowserRouter([
  {
    path: '/',
    element: (
      <AuthRoute>
        <Layout />
      </AuthRoute>
    ),
    children: [
      {
        path: '/',
        element: <Navigate to="/home" replace />,
      },
      {
        path: '/home',
        Component: lazy(() => import('@/pages/Home')),
      },
    ],
  },
])

export default routes
