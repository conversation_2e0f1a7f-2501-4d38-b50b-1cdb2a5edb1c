import React from "react";
import Container from "./container";
import NetworkSituation from "./components/NetworkSituation";
import UserSituation from "./components/UserSituation";
import RegionSelection from "./components/RegionSelection";
import Subway from "./components/Subway";

function Home() {
  return (
    <React.Fragment>
      <div className="size-full text-[80px] flex flex-col relative">
        <div className="w-full flex items-center h-[180px] bg-[#0D2333] absolute top-0 left-0 opacity-90 z-[2]">
          <div className="text-[#fff] text-[80px] font-bold px-[100px]">中国联通重保应急管理系统</div>
        </div>
        <div className="w-full h-full bg-[#101D26] relative">
          <NetworkSituation />
          <UserSituation />
          <RegionSelection />
          <Subway />
        </div>
      </div>
    </React.Fragment>
  )
}

export default () => {
  return (
    <Container.Provider>
      <Home />
    </Container.Provider>
  )
}