import { useMount, useUnmount } from "ahooks"; // 新增useUnmount用于清理
import { useRef } from "react";
import styles from "./index.module.less";

function Subway() {
  const mapRef = useRef<HTMLDivElement>(null);
  const subwayRef = useRef<any | null>(null);

  useMount(() => {
    if (!window.BMapSub || !window.BMapSub.SubwayCitiesList) {
      console.error("请确认已引入百度地铁图API的JS和CSS");
      return;
    }

    const subwayCityName = "北京";
    const targetCity = window.BMapSub.SubwayCitiesList.find(
      (city: any) => city.name === subwayCityName
    );

    if (!targetCity) {
      console.error(`城市${subwayCityName}不在支持列表中`);
      return;
    }
    if (!mapRef.current) {
      return;
    }

    try {
      const subwayInstance = new window.BMapSub.Subway(
        mapRef.current,
        targetCity.citycode
      );
      subwayRef.current = subwayInstance;
      subwayInstance.setZoom(0.5);
      subwayInstance.setCenter("安定门");
    } catch (err) {
      console.error("初始化错误：", err);
    }
  });

  useUnmount(() => {
    if (subwayRef.current) {
      subwayRef.current.removeEventListener("onload", () => {});
      subwayRef.current = null;
    }
  });

  return (
    <div className={styles.subContainer}>
      <div className={styles.subMain} ref={mapRef}></div>
    </div>
  );
}

export default Subway;
