import { useMount, useUnmount } from "ahooks";
import { useRef, useState, useEffect } from "react";
import styles from "./index.module.less";

function Subway() {
  const mapRef = useRef<HTMLDivElement>(null);
  const subwayRef = useRef<any | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // 获取ReactScaleScreen的缩放信息
  const getScaleInfo = () => {
    // 向上查找ReactScaleScreen容器
    let element = mapRef.current?.parentElement;
    while (element) {
      const transform = window.getComputedStyle(element).transform;
      if (transform && transform !== 'none') {
        // 解析transform matrix获取缩放比例
        const matrix = transform.match(/matrix\(([^)]+)\)/);
        if (matrix) {
          const values = matrix[1].split(',').map(v => parseFloat(v.trim()));
          const scaleX = values[0];
          const scaleY = values[3];
          return { scaleX, scaleY, element };
        }
      }
      element = element.parentElement;
    }
    return { scaleX: 1, scaleY: 1, element: null };
  };

  // 初始化地铁图
  const initSubway = () => {
    if (!window.BMapSub || !window.BMapSub.SubwayCitiesList) {
      console.error("请确认已引入百度地铁图API的JS和CSS");
      return;
    }

    const subwayCityName = "北京";
    const targetCity = window.BMapSub.SubwayCitiesList.find(
      (city: any) => city.name === subwayCityName
    );

    if (!targetCity) {
      console.error(`城市${subwayCityName}不在支持列表中`);
      return;
    }

    if (!mapRef.current) {
      return;
    }

    try {
      // 清理旧的实例
      if (subwayRef.current) {
        subwayRef.current = null;
      }

      // 获取缩放信息
      const { scaleX, scaleY } = getScaleInfo();

      // 获取容器的原始尺寸（未缩放前的尺寸）
      const containerRect = mapRef.current.getBoundingClientRect();
      const originalWidth = containerRect.width / scaleX;
      const originalHeight = containerRect.height / scaleY;

      console.log(`容器原始尺寸: ${originalWidth}x${originalHeight}, 缩放比例: ${scaleX}x${scaleY}`);

      // 临时修改容器样式，让百度SDK读取到正确的尺寸
      const originalStyle = {
        width: mapRef.current.style.width,
        height: mapRef.current.style.height,
        transform: mapRef.current.style.transform,
        transformOrigin: mapRef.current.style.transformOrigin
      };

      // 设置为实际的视觉尺寸
      mapRef.current.style.width = `${containerRect.width}px`;
      mapRef.current.style.height = `${containerRect.height}px`;
      mapRef.current.style.transform = 'none';

      // 初始化地铁图
      const subwayInstance = new window.BMapSub.Subway(
        mapRef.current,
        targetCity.citycode
      );

      // 恢复原始样式
      mapRef.current.style.width = originalStyle.width;
      mapRef.current.style.height = originalStyle.height;
      mapRef.current.style.transform = originalStyle.transform;
      mapRef.current.style.transformOrigin = originalStyle.transformOrigin;

      subwayRef.current = subwayInstance;

      // 根据缩放比例调整地铁图的缩放级别
      const avgScale = (scaleX + scaleY) / 2;
      const baseZoom = 0.5;
      const adjustedZoom = Math.max(0.1, Math.min(2, baseZoom * avgScale));

      // 等待地铁图加载完成后设置参数
      const setupSubway = () => {
        try {
          subwayInstance.setZoom(adjustedZoom);
          subwayInstance.setCenter("安定门");
          setIsInitialized(true);
          console.log(`地铁图初始化完成，调整后缩放: ${adjustedZoom}`);
        } catch (error) {
          console.error("地铁图设置失败：", error);
        }
      };

      // 监听地铁图加载完成事件
      if (subwayInstance.addEventListener) {
        subwayInstance.addEventListener('subwayloaded', setupSubway);
      }

      // 备用方案：延迟执行
      setTimeout(setupSubway, 500);

    } catch (err) {
      console.error("地铁图初始化错误：", err);
    }
  };

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      if (isInitialized) {
        // 重新初始化地铁图
        setIsInitialized(false);
        setTimeout(initSubway, 100);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isInitialized]);

  useMount(() => {
    // 延迟初始化，确保ReactScaleScreen已经应用了缩放
    const timer = setTimeout(initSubway, 300);
    return () => clearTimeout(timer);
  });

  useUnmount(() => {
    if (subwayRef.current) {
      try {
        subwayRef.current.removeEventListener?.("subwayloaded", () => {});
      } catch (e) {
        // 忽略清理错误
      }
      subwayRef.current = null;
    }
  });

  return (
    <div className={styles.subContainer}>
      <div className={styles.subMain} ref={mapRef}>
        {!isInitialized && (
          <div className={styles.loading}>
            地铁图加载中...
          </div>
        )}
      </div>
    </div>
  );
}

export default Subway;
