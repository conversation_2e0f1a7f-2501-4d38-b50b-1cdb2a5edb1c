import { useMount, useUnmount } from "ahooks";
import { useRef, useState } from "react";
import styles from "./index.module.less";

function Subway() {
  const mapRef = useRef<HTMLDivElement>(null);
  const subwayRef = useRef<any | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 初始化地铁图
  const initSubway = async () => {
    try {
      setError(null);

      if (!window.BMapSub || !window.BMapSub.SubwayCitiesList) {
        throw new Error("百度地铁图API未加载");
      }

      const subwayCityName = "北京";
      const targetCity = window.BMapSub.SubwayCitiesList.find(
        (city: any) => city.name === subwayCityName
      );

      if (!targetCity) {
        throw new Error(`城市${subwayCityName}不在支持列表中`);
      }

      if (!mapRef.current) {
        throw new Error("地图容器未找到");
      }

      // 清理旧的实例
      if (subwayRef.current) {
        try {
          subwayRef.current.removeEventListener?.("subwayloaded", () => {});
        } catch (e) {
          // 忽略清理错误
        }
        subwayRef.current = null;
      }

      // 清空容器内容
      mapRef.current.innerHTML = '';

      console.log("开始初始化地铁图...");

      // 创建地铁图实例
      const subwayInstance = new window.BMapSub.Subway(
        mapRef.current,
        targetCity.citycode
      );

      subwayRef.current = subwayInstance;

      // 设置地铁图参数
      const setupSubway = () => {
        try {
          if (subwayRef.current) {
            // 设置合适的缩放级别和中心点
            subwayRef.current.setZoom(0.4);
            subwayRef.current.setCenter("安定门");
            setIsInitialized(true);
            console.log("地铁图初始化完成");
          }
        } catch (error) {
          console.error("地铁图设置失败：", error);
          setError("地铁图设置失败");
          setIsInitialized(true); // 即使失败也标记为已初始化，避免无限重试
        }
      };

      // 监听地铁图加载完成事件
      let hasSetup = false;
      const handleSubwayLoaded = () => {
        if (!hasSetup) {
          hasSetup = true;
          setupSubway();
        }
      };

      if (subwayInstance.addEventListener) {
        subwayInstance.addEventListener('subwayloaded', handleSubwayLoaded);
      }

      // 备用方案：延迟执行
      setTimeout(() => {
        if (!hasSetup) {
          hasSetup = true;
          setupSubway();
        }
      }, 2000);

    } catch (err) {
      console.error("地铁图初始化错误：", err);
      setError(err instanceof Error ? err.message : "初始化失败");
      setIsInitialized(true);
    }
  };

  // 重试初始化
  const retryInit = () => {
    setIsInitialized(false);
    setError(null);
    setTimeout(initSubway, 100);
  };

  useMount(() => {
    // 延迟初始化，确保DOM已经渲染完成并且ReactScaleScreen已经应用了缩放
    const timer = setTimeout(initSubway, 800);
    return () => clearTimeout(timer);
  });

  useUnmount(() => {
    if (subwayRef.current) {
      try {
        subwayRef.current.removeEventListener?.("subwayloaded", () => {});
      } catch (e) {
        // 忽略清理错误
      }
      subwayRef.current = null;
    }
  });

  return (
    <div className={styles.subContainer}>
      <div className={styles.subMain} ref={mapRef}>
        {!isInitialized && !error && (
          <div className={styles.loading}>
            地铁图加载中...
          </div>
        )}
        {error && (
          <div className={styles.error}>
            <div>{error}</div>
            <button onClick={retryInit} className={styles.retryButton}>
              重试
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default Subway;
