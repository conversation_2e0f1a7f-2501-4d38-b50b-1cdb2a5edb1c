.subContainer {
  width: 100%; // 父容器宽度
  height: 100%; // 固定高度
  position: absolute; // 用于加载提示居中
  top: 0;
  left: 0;
  overflow: hidden; // 防止内容溢出
}

.subMain {
  width: 100%; // 填满父容器
  height: 100%; // 填满父容器
  background: none !important;
  position: relative;

  // 确保地铁图容器能够正确响应尺寸变化
  & > div {
    width: 100% !important;
    height: 100% !important;
  }

  // 修复百度地铁图可能的样式问题
  canvas {
    max-width: 100% !important;
    max-height: 100% !important;
  }

  // 地铁图内部元素样式修复
  :global {
    .subway-container {
      width: 100% !important;
      height: 100% !important;
      position: relative !important;
    }

    .subway-map {
      width: 100% !important;
      height: 100% !important;
    }
  }
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 16px;
  z-index: 10;
}
