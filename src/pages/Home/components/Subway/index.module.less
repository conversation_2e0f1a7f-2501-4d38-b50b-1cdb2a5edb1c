.subContainer {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  background: transparent;
}

.subMain {
  width: 100%;
  height: 100%;
  background: none !important;
  position: relative;

  // 确保地铁图容器能够正确响应尺寸变化
  & > div {
    width: 100% !important;
    height: 100% !important;
    position: relative !important;
    overflow: visible !important;
  }

  // 修复百度地铁图的样式问题
  :global {
    // 地铁图主容器
    .subway-container,
    .subway_container {
      width: 100% !important;
      height: 100% !important;
      position: relative !important;
      overflow: visible !important;
    }

    // 地铁图画布
    .subway-map,
    .subway_map {
      width: 100% !important;
      height: 100% !important;
      position: relative !important;
    }

    // SVG元素
    svg {
      width: 100% !important;
      height: 100% !important;
      max-width: none !important;
      max-height: none !important;
    }

    // Canvas元素
    canvas {
      max-width: none !important;
      max-height: none !important;
    }

    // 控件样式修复
    .subway-control,
    .subway_control {
      position: absolute !important;
      z-index: 1000 !important;
    }
  }
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 16px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px 20px;
  border-radius: 4px;
}

.error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 14px;
  z-index: 10;
  background: rgba(220, 53, 69, 0.8);
  padding: 15px 20px;
  border-radius: 4px;
  text-align: center;

  div {
    margin-bottom: 10px;
  }
}

.retryButton {
  background: #fff;
  color: #dc3545;
  border: none;
  padding: 5px 15px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;

  &:hover {
    background: #f8f9fa;
  }
}
