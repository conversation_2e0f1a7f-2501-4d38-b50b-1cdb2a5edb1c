import React, { useEffect, useRef } from 'react';

interface BaiduMapProps {
  width?: string;
  height?: string;
  center?: { lng: number; lat: number };
  zoom?: number;
  mapType?: 'normal' | 'satellite';
  className?: string;
  onMapChange?: (center: { lng: number; lat: number }, zoom: number) => void;
}

const BaiduMap: React.FC<BaiduMapProps> = ({
  width = '100%',
  height = '400px',
  center = { lng: 108.213486, lat: 35.194457 }, // 默认中心点
  zoom = 5,
  mapType = 'normal',
  className = '',
  onMapChange
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const lastPropsRef = useRef<{ center: { lng: number; lat: number }, zoom: number, mapType: string } | null>(null);

  useEffect(() => {
    // 检查百度地图API是否已加载
    if (typeof window.BMapGL === 'undefined') {
      console.error('百度地图API未加载，请确保在index.html中引入了百度地图API');
      return;
    }

    if (mapRef.current && !mapInstanceRef.current) {
      // 创建地图实例
      const map = new window.BMapGL.Map(mapRef.current);

      // 设置中心点坐标
      const point = new window.BMapGL.Point(center.lng, center.lat);

      // 初始化地图，设置中心点坐标和地图级别
      map.centerAndZoom(point, zoom);

      // 开启鼠标滚轮缩放
      map.enableScrollWheelZoom(true);

      // 添加缩放控件
      map.addControl(new window.BMapGL.ZoomControl());

      // 添加比例尺控件
      map.addControl(new window.BMapGL.ScaleControl());

      // 设置地图类型
      if (mapType === 'satellite') {
        map.setMapType(window.BMAP_SATELLITE_MAP);
      } else {
        map.setMapType(window.BMAP_NORMAL_MAP);
      }

      // 设置自定义地图样式 - 隐藏国外地名
      try {
        const mapStyle = {
          styleJson: [
            // 基础地形颜色 - 黑色系
            {
              "featureType": "land",
              "elementType": "geometry",
              "stylers": {
                "visibility": "on",
                "color": "#1a1a1aff"  // 深灰黑色陆地
              }
            },
            {
              "featureType": "water",
              "elementType": "geometry",
              "stylers": {
                "visibility": "on",
                "color": "#0d1117ff"  // 深黑色水体
              }
            },
            {
              "featureType": "green",
              "elementType": "geometry",
              "stylers": {
                "visibility": "on",
                "color": "#2d2d2dff"  // 深灰色绿地
              }
            },
            // 建筑物
            {
              "featureType": "building",
              "elementType": "geometry.fill",
              "stylers": {
                "color": "#333333ff"  // 深灰色建筑
              }
            },
            {
              "featureType": "building",
              "elementType": "geometry.stroke",
              "stylers": {
                "color": "#444444ff"  // 建筑边框
              }
            },
            // 道路
            {
              "featureType": "highway",
              "elementType": "geometry.fill",
              "stylers": {
                "color": "#555555ff"  // 高速公路
              }
            },
            {
              "featureType": "arterial",
              "elementType": "geometry.fill",
              "stylers": {
                "color": "#444444ff"  // 主干道
              }
            },
            {
              "featureType": "local",
              "elementType": "geometry.fill",
              "stylers": {
                "color": "#333333ff"  // 次要道路
              }
            },
            // 标签控制
            {
              "featureType": "continent",
              "elementType": "labels",
              "stylers": {
                "visibility": "off"  // 隐藏大洲标签
              }
            },
            {
              "featureType": "country",
              "elementType": "labels",
              "stylers": {
                "visibility": "off"  // 隐藏国家标签
              }
            },
            {
              "featureType": "province",
              "elementType": "labels.text.fill",
              "stylers": {
                "visibility": "off",
                "color": "#ccccccff"  // 浅灰色省份标签
              }
            },
            {
              "featureType": "city",
              "elementType": "labels.text.fill",
              "stylers": {
                "color": "#bbbbbbff"  // 浅灰色城市标签
              }
            },
            {
              "featureType": "town",
              "elementType": "labels.text.fill",
              "stylers": {
                "color": "#aaaaaa"  // 浅灰色城镇标签
              }
            },
            {
              "featureType": "district",
              "elementType": "labels.text.fill",
              "stylers": {
                "color": "#999999ff"  // 灰色区县标签
              }
            },
            // 道路标签
            {
              "featureType": "highway",
              "elementType": "labels.text.fill",
              "stylers": {
                "color": "#ffffffff"  // 白色高速公路标签
              }
            },
            {
              "featureType": "arterial",
              "elementType": "labels.text.fill",
              "stylers": {
                "color": "#ddddddff"  // 浅灰色主干道标签
              }
            }
          ]
        };

        if (map.setMapStyleV2) {
          map.setMapStyleV2(mapStyle);
        }
      } catch {
        console.log('地图样式设置失败，使用默认样式');
      }

      // 添加地图变化事件监听器
      if (onMapChange) {
        // 监听地图移动事件
        map.addEventListener('moveend', () => {
          const center = map.getCenter();
          const zoom = map.getZoom();
          onMapChange({ lng: center.lng, lat: center.lat }, zoom);
        });

        // 监听缩放事件
        map.addEventListener('zoomend', () => {
          const center = map.getCenter();
          const zoom = map.getZoom();
          onMapChange({ lng: center.lng, lat: center.lat }, zoom);
        });
      }

      mapInstanceRef.current = map;
      lastPropsRef.current = { center, zoom, mapType };
    }

    // 如果地图已存在，检查是否需要更新
    if (mapInstanceRef.current) {
      const lastProps = lastPropsRef.current;
      const needsUpdate = !lastProps ||
        lastProps.center.lng !== center.lng ||
        lastProps.center.lat !== center.lat ||
        lastProps.zoom !== zoom ||
        lastProps.mapType !== mapType;

      if (needsUpdate) {
        // 设置中心点坐标
        const point = new window.BMapGL.Point(center.lng, center.lat);

        // 设置地图类型
        if (mapType === 'satellite') {
          mapInstanceRef.current.setMapType(window.BMAP_SATELLITE_MAP);
        } else {
          mapInstanceRef.current.setMapType(window.BMAP_NORMAL_MAP);

          // 只在普通地图模式下应用自定义样式
          try {
            const mapStyle = {
              styleJson: [
                // 基础地形颜色 - 黑色系
                {
                  "featureType": "land",
                  "elementType": "geometry",
                  "stylers": {
                    "visibility": "on",
                    "color": "#1a1a1aff"
                  }
                },
                {
                  "featureType": "water",
                  "elementType": "geometry",
                  "stylers": {
                    "visibility": "on",
                    "color": "#0d1117ff"
                  }
                },
                {
                  "featureType": "green",
                  "elementType": "geometry",
                  "stylers": {
                    "visibility": "on",
                    "color": "#2d2d2dff"
                  }
                },
                // 建筑物
                {
                  "featureType": "building",
                  "elementType": "geometry.fill",
                  "stylers": {
                    "color": "#333333ff"
                  }
                },
                {
                  "featureType": "building",
                  "elementType": "geometry.stroke",
                  "stylers": {
                    "color": "#444444ff"
                  }
                },
                // 道路
                {
                  "featureType": "highway",
                  "elementType": "geometry.fill",
                  "stylers": {
                    "color": "#555555ff"
                  }
                },
                {
                  "featureType": "arterial",
                  "elementType": "geometry.fill",
                  "stylers": {
                    "color": "#444444ff"
                  }
                },
                {
                  "featureType": "local",
                  "elementType": "geometry.fill",
                  "stylers": {
                    "color": "#333333ff"
                  }
                },
                // 标签控制
                {
                  "featureType": "continent",
                  "elementType": "labels",
                  "stylers": {
                    "visibility": "off"
                  }
                },
                {
                  "featureType": "country",
                  "elementType": "labels",
                  "stylers": {
                    "visibility": "off"
                  }
                },
                {
                  "featureType": "province",
                  "elementType": "labels.text.fill",
                  "stylers": {
                    "color": "#ccccccff"
                  }
                },
                {
                  "featureType": "city",
                  "elementType": "labels.text.fill",
                  "stylers": {
                    "color": "#bbbbbbff"
                  }
                },
                {
                  "featureType": "town",
                  "elementType": "labels.text.fill",
                  "stylers": {
                    "color": "#aaaaaa"
                  }
                },
                {
                  "featureType": "district",
                  "elementType": "labels.text.fill",
                  "stylers": {
                    "color": "#999999ff"
                  }
                },
                // 道路标签
                {
                  "featureType": "highway",
                  "elementType": "labels.text.fill",
                  "stylers": {
                    "color": "#ffffffff"
                  }
                },
                {
                  "featureType": "arterial",
                  "elementType": "labels.text.fill",
                  "stylers": {
                    "color": "#ddddddff"
                  }
                }
              ]
            };

            if (mapInstanceRef.current.setMapStyleV2) {
              mapInstanceRef.current.setMapStyleV2(mapStyle);
            }
          } catch {
            console.log('地图样式更新失败');
          }
        }

        // 设置中心点和缩放级别
        mapInstanceRef.current.centerAndZoom(point, zoom);

        // 更新最后的 props 记录
        lastPropsRef.current = { center, zoom, mapType };
      }
    }

    // 清理函数
    return () => {
      // 注意：不要在这里销毁地图实例，因为我们需要复用它
    };
  }, [center, zoom, mapType, onMapChange]);

  return (
    <div
      ref={mapRef}
      className={className}
      style={{ width, height, margin: 0, padding: 0 }}
    />
  );
};

export default BaiduMap;
