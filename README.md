# 百度地图应用

基于 Vite + React + TypeScript + TailwindCSS + 百度地图 GL 的现代化地图应用。

## 技术栈

- **Vite 6.x** - 快速的前端构建工具
- **React 19** - 用户界面库
- **TypeScript** - 类型安全的 JavaScript
- **TailwindCSS** - 实用优先的 CSS 框架
- **pnpm** - 快速、节省磁盘空间的包管理器
- **百度地图 GL** - 百度地图 WebGL 版本 API
- **Node.js 22.x** - 运行时环境

## 功能特性

- 🗺️ 全屏百度地图显示
- 🛰️ 支持普通地图和卫星地图切换
- 📍 默认显示北京市中心
- 🚄 一键查看北京南站卫星图
- 🔧 TypeScript 类型支持
- ⚡ 快速热重载

## 开始使用

### 前置要求

- Node.js 22+ (推荐使用 nvm 管理版本)
- pnpm

### 环境设置

如果你使用的不是 Node.js 22.x，请使用 nvm 切换：

```bash
# 安装并使用 Node.js 22
nvm install 22
nvm use 22
```

### 安装依赖

```bash
pnpm install
```

### 配置百度地图 API Key

✅ **API Key 已配置完成**

项目已经配置了有效的百度地图 API Key，可以直接使用。如果需要更换 API Key：

1. 访问 [百度地图开放平台](https://lbsyun.baidu.com/) 注册账号
2. 创建应用获取 API Key
3. 在 `index.html` 文件中替换现有的 API Key：

```html
<script type="text/javascript" src="https://api.map.baidu.com/api?v=1.0&type=webgl&ak=你的新API_KEY"></script>
```

### 启动开发服务器

```bash
pnpm dev
```

应用将在 http://localhost:5173 或其他可用端口启动。

### 构建生产版本

```bash
pnpm build
```

### 预览生产构建

```bash
pnpm preview
```

## 项目结构

```
src/
├── components/          # React 组件
│   └── BaiduMap.tsx    # 百度地图组件
├── types/              # TypeScript 类型定义
│   └── baidu-map.d.ts  # 百度地图类型定义
├── App.tsx             # 主应用组件
├── main.tsx            # 应用入口
└── index.css           # 全局样式
```

## 使用说明

### 应用功能

**主要功能：**
- **全屏地图显示**：地图占据整个屏幕，提供最佳的地图浏览体验
- **北京市中心默认视图**：应用启动时显示北京市中心区域
- **北京南站卫星图**：点击右上角按钮可快速切换到北京南站的卫星地图视图
- **返回市中心**：可以随时返回到北京市中心的普通地图视图

**操作说明：**
- 鼠标滚轮：缩放地图
- 鼠标拖拽：移动地图
- 右上角按钮：切换到北京南站卫星图
- 返回按钮：回到北京市中心

### 百度地图组件属性

- `center`: 地图中心点坐标 `{ lng: number, lat: number }`
- `zoom`: 地图缩放级别 (3-19)
- `mapType`: 地图类型 `'normal' | 'satellite'`
- `width`: 地图宽度，默认 "100%"
- `height`: 地图高度，默认 "400px"
- `className`: 自定义 CSS 类名

## 开发指南

### 添加新的地图功能

1. 在 `BaiduMap.tsx` 组件中添加新的地图控件或功能
2. 更新 TypeScript 类型定义
3. 在主应用中使用新功能

### 样式定制

项目使用 TailwindCSS，你可以：
- 修改 `tailwind.config.js` 自定义主题
- 在组件中使用 Tailwind 类名
- 添加自定义 CSS 到 `src/index.css`

## 注意事项

- 确保使用 Node.js 22+ 版本以获得最佳兼容性
- 百度地图 API Key 是必需的，否则地图无法正常显示
- 项目使用 Vite 6.x 版本以确保稳定性

## 许可证

MIT
