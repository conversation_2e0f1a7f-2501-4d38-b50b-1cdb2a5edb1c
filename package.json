{"name": "baidu-map-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "~5.6.1", "@antfu/eslint-config": "^5.2.1", "@types/bmapgl": "^0.0.7", "ahooks": "^3.9.3", "antd": "^5.27.1", "autoprefixer": "^10.4.21", "immer": "^10.1.1", "less": "^4.4.1", "less-loader": "^12.3.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-bmap": "^1.0.131", "react-bmapgl": "^1.0.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.1", "tailwindcss": "^4.1.11", "unstated-next": "^1.1.0", "use-sync-external-store": "^1.5.0", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.32.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.3.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^6.3.5"}}